-- =====================================================
-- FIX APPEARANCE SETTINGS TABLE ACCESS ISSUES
-- =====================================================
-- This script diagnoses and fixes appearance_settings table access problems

-- =====================================================
-- 1. DIAGNOSTIC CHECKS
-- =====================================================

-- Check if table exists
SELECT 
    'Table Existence' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'appearance_settings' 
            AND table_schema = 'public'
        ) 
        THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

-- Check RLS status
SELECT 
    'RLS Status' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'appearance_settings';

-- Check policies
SELECT 
    'RLS Policies' as check_type,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'appearance_settings';

-- =====================================================
-- 2. RECREATE TABLE IF MISSING
-- =====================================================

-- Drop and recreate table to ensure it exists
DROP TABLE IF EXISTS appearance_settings CASCADE;

-- Create appearance_settings table
CREATE TABLE appearance_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT appearance_settings_key_not_empty CHECK (LENGTH(TRIM(setting_key)) > 0)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_appearance_settings_key ON appearance_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_appearance_settings_updated_at ON appearance_settings(updated_at DESC);

-- =====================================================
-- 3. SETUP RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE appearance_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable all operations for application" ON appearance_settings;
DROP POLICY IF EXISTS "Enable read access for all users" ON appearance_settings;
DROP POLICY IF EXISTS "Enable write access for all users" ON appearance_settings;

-- Create permissive policies for all operations
CREATE POLICY "Enable read access for all users" 
    ON appearance_settings FOR SELECT 
    USING (true);

CREATE POLICY "Enable insert access for all users" 
    ON appearance_settings FOR INSERT 
    WITH CHECK (true);

CREATE POLICY "Enable update access for all users" 
    ON appearance_settings FOR UPDATE 
    USING (true) 
    WITH CHECK (true);

CREATE POLICY "Enable delete access for all users" 
    ON appearance_settings FOR DELETE 
    USING (true);

-- =====================================================
-- 4. CREATE UPDATE TRIGGER
-- =====================================================

-- Create or replace the update function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger
DROP TRIGGER IF EXISTS update_appearance_settings_updated_at ON appearance_settings;
CREATE TRIGGER update_appearance_settings_updated_at
    BEFORE UPDATE ON appearance_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. INSERT DEFAULT DATA
-- =====================================================

-- Insert default appearance settings
INSERT INTO appearance_settings (setting_key, setting_value) 
VALUES (
    'landing_page_background',
    '{
        "backgroundType": "gradient",
        "gradientStyle": "hero-gradient",
        "customColors": {
            "primary": "#22c55e",
            "secondary": "#facc15"
        },
        "backgroundImage": null,
        "backgroundImagePublicId": null
    }'::jsonb
)
ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- =====================================================
-- 6. VERIFICATION
-- =====================================================

-- Verify table structure
SELECT 
    'Table Structure' as check_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'appearance_settings' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Verify RLS policies
SELECT 
    'Final RLS Check' as check_type,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE tablename = 'appearance_settings';

-- Verify data
SELECT 
    'Data Verification' as check_type,
    setting_key,
    setting_value,
    created_at
FROM appearance_settings;

-- Test basic operations
DO $$
DECLARE
    test_result JSONB;
BEGIN
    -- Test SELECT
    SELECT setting_value INTO test_result 
    FROM appearance_settings 
    WHERE setting_key = 'landing_page_background';
    
    IF test_result IS NOT NULL THEN
        RAISE NOTICE '✅ SELECT operation successful';
    ELSE
        RAISE NOTICE '❌ SELECT operation failed - no data found';
    END IF;
    
    -- Test UPDATE
    UPDATE appearance_settings 
    SET updated_at = NOW() 
    WHERE setting_key = 'landing_page_background';
    
    IF FOUND THEN
        RAISE NOTICE '✅ UPDATE operation successful';
    ELSE
        RAISE NOTICE '❌ UPDATE operation failed';
    END IF;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE '❌ Database operations failed: %', SQLERRM;
END $$;

SELECT '🎉 APPEARANCE SETTINGS TABLE FIXED!' as final_status;
