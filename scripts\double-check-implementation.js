/**
 * Double Check Implementation
 * Comprehensive verification of Cloudinary-only approach
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function doubleCheckImplementation() {
  console.log('🔍 DOUBLE CHECKING IMPLEMENTATION...\n')

  const issues = []
  const successes = []

  try {
    // Check 1: Database Connection
    console.log('1️⃣ Database Connection...')
    try {
      const { data, error } = await supabase
        .from('appearance_settings')
        .select('count', { count: 'exact', head: true })

      if (error) throw error
      successes.push('Database connection working')
      console.log('   ✅ Database connection successful')
    } catch (error) {
      issues.push(`Database connection failed: ${error.message}`)
      console.log('   ❌ Database connection failed:', error.message)
    }

    // Check 2: Appearance Service Implementation
    console.log('\n2️⃣ Appearance Service Implementation...')
    const appearanceServicePath = path.join(process.cwd(), 'src/lib/appearance-service.ts')
    
    if (fs.existsSync(appearanceServicePath)) {
      const content = fs.readFileSync(appearanceServicePath, 'utf8')
      
      // Check for localStorage removal
      if (content.includes('localStorage.setItem') || content.includes('localStorage.getItem')) {
        issues.push('appearance-service.ts still contains localStorage code')
        console.log('   ❌ localStorage code still present')
      } else {
        successes.push('localStorage removed from appearance service')
        console.log('   ✅ localStorage code removed')
      }

      // Check for Cloudinary-only approach
      if (content.includes('Database + Cloudinary only')) {
        successes.push('Cloudinary-only approach implemented')
        console.log('   ✅ Cloudinary-only approach confirmed')
      } else {
        issues.push('Cloudinary-only approach not clearly implemented')
        console.log('   ❌ Cloudinary-only approach unclear')
      }

      // Check for cleanup import
      if (content.includes('LocalStorageCleanup')) {
        successes.push('LocalStorage cleanup integrated')
        console.log('   ✅ LocalStorage cleanup integrated')
      } else {
        issues.push('LocalStorage cleanup not integrated')
        console.log('   ❌ LocalStorage cleanup missing')
      }
    } else {
      issues.push('appearance-service.ts file not found')
      console.log('   ❌ appearance-service.ts not found')
    }

    // Check 3: Cleanup Implementation
    console.log('\n3️⃣ LocalStorage Cleanup Implementation...')
    const cleanupPath = path.join(process.cwd(), 'src/lib/cleanup-localstorage.ts')
    
    if (fs.existsSync(cleanupPath)) {
      successes.push('LocalStorage cleanup file exists')
      console.log('   ✅ cleanup-localstorage.ts exists')
    } else {
      issues.push('LocalStorage cleanup file missing')
      console.log('   ❌ cleanup-localstorage.ts missing')
    }

    // Check 4: Appearance Sync Implementation
    console.log('\n4️⃣ Appearance Sync Implementation...')
    const syncPath = path.join(process.cwd(), 'src/lib/appearance-sync.ts')
    
    if (fs.existsSync(syncPath)) {
      const syncContent = fs.readFileSync(syncPath, 'utf8')
      
      // Check if localStorage storage events are removed
      if (syncContent.includes('handleStorageChange')) {
        issues.push('appearance-sync.ts still has localStorage storage handlers')
        console.log('   ❌ localStorage storage handlers still present')
      } else {
        successes.push('localStorage storage handlers removed from sync')
        console.log('   ✅ localStorage storage handlers removed')
      }
    } else {
      issues.push('appearance-sync.ts file not found')
      console.log('   ❌ appearance-sync.ts not found')
    }

    // Check 5: Database Settings
    console.log('\n5️⃣ Database Settings...')
    try {
      const { data: settings, error } = await supabase
        .from('appearance_settings')
        .select('*')
        .eq('setting_key', 'landing_page_background')
        .single()

      if (error) throw error

      console.log('   ✅ Settings found in database')
      console.log(`      Background Type: ${settings.setting_value.backgroundType}`)
      
      if (settings.setting_value.noLocalStorage) {
        successes.push('Database marked as localStorage-free')
        console.log('   ✅ Database marked as localStorage-free')
      } else {
        issues.push('Database not marked as localStorage-free')
        console.log('   ❌ Database not marked as localStorage-free')
      }

      if (settings.setting_value.cloudinaryOnly) {
        successes.push('Database marked as Cloudinary-only')
        console.log('   ✅ Database marked as Cloudinary-only')
      } else {
        issues.push('Database not marked as Cloudinary-only')
        console.log('   ❌ Database not marked as Cloudinary-only')
      }

    } catch (error) {
      issues.push(`Database settings check failed: ${error.message}`)
      console.log('   ❌ Database settings check failed:', error.message)
    }

    // Check 6: Cloudinary Configuration
    console.log('\n6️⃣ Cloudinary Configuration...')
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
    const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET
    const apiKey = process.env.CLOUDINARY_API_KEY
    const apiSecret = process.env.CLOUDINARY_API_SECRET

    if (cloudName && uploadPreset && apiKey && apiSecret) {
      successes.push('Cloudinary fully configured')
      console.log('   ✅ Cloudinary fully configured')
      console.log(`      Cloud Name: ${cloudName}`)
      console.log(`      Upload Preset: ${uploadPreset}`)
    } else {
      issues.push('Cloudinary configuration incomplete')
      console.log('   ❌ Cloudinary configuration incomplete')
    }

    // Check 7: Landing Page Implementation
    console.log('\n7️⃣ Landing Page Implementation...')
    const landingPath = path.join(process.cwd(), 'src/app/landing/page.tsx')
    
    if (fs.existsSync(landingPath)) {
      const landingContent = fs.readFileSync(landingPath, 'utf8')
      
      if (landingContent.includes('AppearanceSync.addListener')) {
        successes.push('Landing page has real-time sync')
        console.log('   ✅ Landing page has real-time sync')
      } else {
        issues.push('Landing page missing real-time sync')
        console.log('   ❌ Landing page missing real-time sync')
      }
    } else {
      issues.push('Landing page file not found')
      console.log('   ❌ Landing page file not found')
    }

    // Check 8: Settings Component Implementation
    console.log('\n8️⃣ Settings Component Implementation...')
    const settingsPath = path.join(process.cwd(), 'src/components/Settings.tsx')
    
    if (fs.existsSync(settingsPath)) {
      const settingsContent = fs.readFileSync(settingsPath, 'utf8')
      
      if (settingsContent.includes('AppearanceSync.addListener')) {
        successes.push('Settings component has real-time sync')
        console.log('   ✅ Settings component has real-time sync')
      } else {
        issues.push('Settings component missing real-time sync')
        console.log('   ❌ Settings component missing real-time sync')
      }
    } else {
      issues.push('Settings component file not found')
      console.log('   ❌ Settings component file not found')
    }

    // Final Report
    console.log('\n' + '='.repeat(60))
    console.log('📊 DOUBLE CHECK RESULTS')
    console.log('='.repeat(60))

    console.log(`\n✅ SUCCESSES (${successes.length}):`)
    successes.forEach(success => console.log(`   • ${success}`))

    if (issues.length > 0) {
      console.log(`\n❌ ISSUES FOUND (${issues.length}):`)
      issues.forEach(issue => console.log(`   • ${issue}`))
    } else {
      console.log('\n🎉 NO ISSUES FOUND!')
    }

    console.log('\n' + '='.repeat(60))
    
    if (issues.length === 0) {
      console.log('🎯 IMPLEMENTATION STATUS: ✅ PERFECT')
      console.log('🚀 READY FOR PRODUCTION')
      console.log('\n📋 FINAL ARCHITECTURE:')
      console.log('   ☁️  Cloudinary: Exclusive image storage')
      console.log('   🗄️  Database: Settings persistence only')
      console.log('   🔄 Real-time: Cross-port synchronization')
      console.log('   🚫 localStorage: Completely removed')
      return true
    } else {
      console.log('⚠️  IMPLEMENTATION STATUS: NEEDS ATTENTION')
      console.log(`📝 ISSUES TO FIX: ${issues.length}`)
      return false
    }

  } catch (error) {
    console.log('❌ Double check failed with error:', error.message)
    return false
  }
}

// Run the double check
doubleCheckImplementation().then(success => {
  process.exit(success ? 0 : 1)
})
