'use client'

import { User, Camera } from 'lucide-react'
import Image from 'next/image'
import { useState, useEffect } from 'react'

import { Customer } from '@/lib/supabase'
import { profilePictureEvents } from '@/lib/profile-picture-events'

interface CustomerAvatarProps {
  profilePictureUrl?: string | undefined
  customerName: string
  customerFamilyName: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showEditButton?: boolean
  onEditClick?: () => void
  className?: string
  forceRefresh?: boolean // Add prop to force cache refresh
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
}

const iconSizes = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-6 w-6',
  xl: 'h-8 w-8'
}

const editButtonSizes = {
  sm: 'w-5 h-5',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-10 h-10'
}

const editIconSizes = {
  sm: 'h-2 w-2',
  md: 'h-3 w-3',
  lg: 'h-3 w-3',
  xl: 'h-4 w-4'
}

export default function CustomerAvatar({
  profilePictureUrl,
  customerName,
  customerFamilyName,
  size = 'md',
  showEditButton = false,
  onEditClick,
  className = '',
  forceRefresh = false
}: CustomerAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageKey, setImageKey] = useState(Date.now())

  // Listen for profile picture updates
  useEffect(() => {
    const unsubscribe = profilePictureEvents.subscribe((updatedCustomerName, updatedCustomerFamilyName, _newImageUrl) => {
      // Check if this avatar is for the updated customer
      if (updatedCustomerName === customerName && updatedCustomerFamilyName === customerFamilyName) {
        // Force re-render by updating the image key
        setImageKey(Date.now())
        // Reset image error state in case the new image loads successfully
        setImageError(false)
      }
    })

    return unsubscribe
  }, [customerName, customerFamilyName])

  // Add cache busting to image URL
  const getImageUrl = () => {
    if (!profilePictureUrl) return ''

    if (forceRefresh) {
      const separator = profilePictureUrl.includes('?') ? '&' : '?'
      return `${profilePictureUrl}${separator}t=${Date.now()}`
    }
    return profilePictureUrl
  }

  // Generate initials from customer name
  const getInitials = () => {
    const firstInitial = customerName.charAt(0).toUpperCase()
    const lastInitial = customerFamilyName.charAt(0).toUpperCase()
    return `${firstInitial}${lastInitial}`
  }

  // Generate a consistent color based on the customer name
  const getAvatarColor = () => {
    const colors = [
      'bg-red-500',
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500',
      'bg-orange-500',
      'bg-cyan-500'
    ]
    
    const nameHash = (customerName + customerFamilyName).split('').reduce((hash, char) => {
      return char.charCodeAt(0) + ((hash << 5) - hash)
    }, 0)
    
    return colors[Math.abs(nameHash) % colors.length]
  }

  const shouldShowImage = profilePictureUrl && !imageError

  return (
    <div className={`relative inline-block ${className}`}>
      <div
        className={`${sizeClasses[size]} rounded-full overflow-hidden border-2 border-white shadow-md transition-all duration-300 hover:shadow-lg`}
        style={{
          backgroundColor: shouldShowImage ? 'transparent' : undefined
        }}
      >
        {shouldShowImage ? (
          <Image
            src={getImageUrl()}
            alt={`${customerName} ${customerFamilyName}`}
            width={size === 'xl' ? 96 : size === 'lg' ? 64 : size === 'md' ? 48 : 32}
            height={size === 'xl' ? 96 : size === 'lg' ? 64 : size === 'md' ? 48 : 32}
            className="w-full h-full object-cover profile-picture"
            quality={95}
            priority={true}
            onError={() => setImageError(true)}
            key={`${imageKey}-${forceRefresh ? Date.now() : profilePictureUrl}`} // Force re-render when image updates
          />
        ) : (
          <div
            className={`w-full h-full flex items-center justify-center text-white font-semibold ${getAvatarColor()}`}
          >
            {getInitials() ? (
              <span className={`${
                size === 'xl' ? 'text-2xl' : 
                size === 'lg' ? 'text-lg' : 
                size === 'md' ? 'text-sm' : 
                'text-xs'
              }`}>
                {getInitials()}
              </span>
            ) : (
              <User className={iconSizes[size]} />
            )}
          </div>
        )}
      </div>

      {/* Edit Button */}
      {showEditButton && onEditClick && (
        <button
          onClick={onEditClick}
          className={`absolute -bottom-1 -right-1 ${editButtonSizes[size]} bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-all duration-200 hover:scale-110 shadow-md hover:shadow-lg flex items-center justify-center`}
          title="Edit Profile Picture"
        >
          <Camera className={editIconSizes[size]} />
        </button>
      )}
    </div>
  )
}

// Hook for managing customer profile pictures
export function useCustomerProfile() {
  const [customers, setCustomers] = useState<Map<string, Customer>>(new Map())

  const getCustomerProfile = async (customerName: string, customerFamilyName: string) => {
    const key = `${customerName}_${customerFamilyName}`

    if (customers.has(key)) {
      return customers.get(key)
    }

    try {
      const response = await fetch(`/api/customers?search=${encodeURIComponent(`${customerName} ${customerFamilyName}`)}`)
      if (response.ok) {
        const data = await response.json()
        // Check if customers array exists and is an array
        if (data.customers && Array.isArray(data.customers)) {
          const customer = data.customers.find((c: Customer) =>
            c.customer_name === customerName && c.customer_family_name === customerFamilyName
          )

          if (customer) {
            setCustomers(prev => new Map(prev.set(key, customer)))
            return customer
          }
        }
      }
    } catch (error) {
      console.error('Error fetching customer profile:', error)
    }

    return null
  }

  const updateCustomerProfile = (customerName: string, customerFamilyName: string, profile: Customer) => {
    const key = `${customerName}_${customerFamilyName}`
    setCustomers(prev => new Map(prev.set(key, profile)))
  }

  return {
    getCustomerProfile,
    updateCustomerProfile,
    customers
  }
}
