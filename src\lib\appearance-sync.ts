/**
 * Real-time Appearance Settings Synchronization
 * Ensures consistent background settings across all application instances
 */

import { supabase } from './supabase'
import { logger } from './logger'
import type { AppearanceSettings } from './appearance-service'

export class AppearanceSync {
  private static listeners: Set<(settings: AppearanceSettings) => void> = new Set()
  private static subscription: any = null
  private static isInitialized = false

  /**
   * Initialize real-time synchronization
   */
  static initialize() {
    if (this.isInitialized) {
      logger.info('🔄 Appearance sync already initialized')
      return
    }

    try {
      // Subscribe to real-time changes
      this.subscription = supabase
        .channel('appearance_settings_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'appearance_settings',
            filter: 'setting_key=eq.landing_page_background'
          },
          (payload) => {
            logger.info('🔄 Real-time appearance settings change detected', payload)
            this.handleSettingsChange(payload)
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            logger.info('✅ Real-time appearance sync initialized')
            this.isInitialized = true
          } else if (status === 'CHANNEL_ERROR') {
            logger.error('❌ Real-time sync channel error')
          }
        })

      // Listen for custom events within the same tab only
      if (typeof window !== 'undefined') {
        window.addEventListener('appearance-settings-updated', this.handleCustomEvent.bind(this))
      }

    } catch (error) {
      logger.error('Failed to initialize appearance sync', { error })
    }
  }

  /**
   * Cleanup subscriptions
   */
  static cleanup() {
    if (this.subscription) {
      supabase.removeChannel(this.subscription)
      this.subscription = null
    }

    if (typeof window !== 'undefined') {
      window.removeEventListener('appearance-settings-updated', this.handleCustomEvent.bind(this))
    }

    this.listeners.clear()
    this.isInitialized = false
    logger.info('🧹 Appearance sync cleanup completed')
  }

  /**
   * Add a listener for settings changes
   */
  static addListener(callback: (settings: AppearanceSettings) => void) {
    this.listeners.add(callback)
    logger.info(`📡 Added appearance settings listener (${this.listeners.size} total)`)

    // Return cleanup function
    return () => {
      this.listeners.delete(callback)
      logger.info(`📡 Removed appearance settings listener (${this.listeners.size} remaining)`)
    }
  }

  /**
   * Broadcast settings change to all listeners
   */
  static broadcastChange(settings: AppearanceSettings) {
    logger.info('📢 Broadcasting appearance settings change', { 
      backgroundType: settings.backgroundType,
      hasImage: !!settings.backgroundImage,
      listenerCount: this.listeners.size
    })

    // Notify all listeners
    this.listeners.forEach(callback => {
      try {
        callback(settings)
      } catch (error) {
        logger.error('Error in appearance settings listener', { error })
      }
    })

    // Broadcast to same-tab components only (no localStorage)
    if (typeof window !== 'undefined') {
      // Dispatch custom event for same-tab communication
      window.dispatchEvent(new CustomEvent('appearance-settings-updated', {
        detail: settings
      }))
    }
  }

  /**
   * Force sync from database
   */
  static async forceSyncFromDatabase(): Promise<AppearanceSettings | null> {
    try {
      logger.info('🔄 Force syncing appearance settings from database')

      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value')
        .eq('setting_key', 'landing_page_background')
        .single()

      if (error) {
        logger.error('Failed to force sync from database', { error })
        return null
      }

      if (data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings
        logger.info('✅ Force sync successful', { 
          backgroundType: settings.backgroundType,
          hasImage: !!settings.backgroundImage
        })
        
        this.broadcastChange(settings)
        return settings
      }

      return null
    } catch (error) {
      logger.error('Error during force sync', { error })
      return null
    }
  }

  /**
   * Handle real-time database changes
   */
  private static handleSettingsChange(payload: any) {
    try {
      const { new: newRecord, old: oldRecord, eventType } = payload

      if (eventType === 'DELETE') {
        logger.info('🗑️ Appearance settings deleted')
        return
      }

      if (newRecord?.setting_value) {
        const settings = newRecord.setting_value as AppearanceSettings
        logger.info('🔄 Database change detected', { 
          eventType,
          backgroundType: settings.backgroundType,
          hasImage: !!settings.backgroundImage
        })

        this.broadcastChange(settings)
      }
    } catch (error) {
      logger.error('Error handling settings change', { error })
    }
  }



  /**
   * Handle custom events (same-tab communication)
   */
  private static handleCustomEvent(event: CustomEvent) {
    if (event.detail) {
      logger.info('🔄 Same-tab settings change detected')
      // Don't re-broadcast to avoid loops
      this.listeners.forEach(callback => {
        try {
          callback(event.detail)
        } catch (error) {
          logger.error('Error in custom event listener', { error })
        }
      })
    }
  }
}

// Auto-initialize when module loads (client-side only)
if (typeof window !== 'undefined') {
  AppearanceSync.initialize()
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    AppearanceSync.cleanup()
  })
}
