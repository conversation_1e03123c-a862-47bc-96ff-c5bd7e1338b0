/**
 * Clean up localStorage on app initialization
 * Ensures no localStorage data interferes with Cloudinary + Database approach
 */

import { logger } from './logger'

export class LocalStorageCleanup {
  private static readonly KEYS_TO_REMOVE = [
    'appearance-settings-fallback',
    'appearance-settings-sync',
    'landing-page-background',
    'background-settings'
  ]

  /**
   * Clean up localStorage appearance-related data
   */
  static cleanup(): void {
    if (typeof window === 'undefined') return

    try {
      let removedCount = 0
      
      this.KEYS_TO_REMOVE.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key)
          removedCount++
          logger.info(`🧹 Removed localStorage key: ${key}`)
        }
      })

      if (removedCount > 0) {
        logger.info(`✅ Cleaned up ${removedCount} localStorage keys - using Cloudinary + Database only`)
      } else {
        logger.info('✅ localStorage already clean - using Cloudinary + Database only')
      }

    } catch (error) {
      logger.warn('Failed to clean localStorage', { error })
    }
  }

  /**
   * Initialize cleanup on app start
   */
  static initialize(): void {
    if (typeof window === 'undefined') return

    // Clean up immediately
    this.cleanup()

    // Also clean up when the page becomes visible (tab switching)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.cleanup()
      }
    })

    logger.info('🚫 localStorage cleanup initialized - Cloudinary + Database only mode')
  }
}

// Auto-initialize when module loads (client-side only)
if (typeof window !== 'undefined') {
  LocalStorageCleanup.initialize()
}
