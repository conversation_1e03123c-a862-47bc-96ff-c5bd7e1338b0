-- =====================================================
-- VERIFY APPEARANCE SETTINGS TABLE AND DATA
-- =====================================================
-- Quick verification script to check if appearance_settings table exists and has data

-- Check if appearance_settings table exists
SELECT 
    'Table Existence Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'appearance_settings' 
            AND table_schema = 'public'
        ) 
        THEN '✅ appearance_settings table EXISTS' 
        ELSE '❌ appearance_settings table MISSING' 
    END as status;

-- Check table structure
SELECT 
    'Table Structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'appearance_settings' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if data exists
SELECT 
    'Data Check' as check_type,
    COUNT(*) as record_count
FROM appearance_settings;

-- Show all records
SELECT 
    'All Records' as check_type,
    setting_key,
    setting_value,
    created_at,
    updated_at
FROM appearance_settings;

-- Check for specific landing page background setting
SELECT 
    'Landing Page Background Setting' as check_type,
    setting_key,
    setting_value,
    created_at
FROM appearance_settings 
WHERE setting_key = 'landing_page_background';

-- Insert default setting if missing
INSERT INTO appearance_settings (setting_key, setting_value) 
VALUES (
    'landing_page_background',
    '{
        "backgroundType": "gradient",
        "gradientStyle": "hero-gradient",
        "customColors": {
            "primary": "#22c55e",
            "secondary": "#facc15"
        },
        "backgroundImage": null,
        "backgroundImagePublicId": null
    }'::jsonb
)
ON CONFLICT (setting_key) DO NOTHING;

-- Verify insertion
SELECT 
    'After Insert Check' as check_type,
    setting_key,
    setting_value,
    created_at
FROM appearance_settings 
WHERE setting_key = 'landing_page_background';

-- Check RLS policies
SELECT 
    'RLS Policies' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'appearance_settings';

-- Check if RLS is enabled
SELECT 
    'RLS Status' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'appearance_settings';

SELECT '✅ VERIFICATION COMPLETE' as final_status;
