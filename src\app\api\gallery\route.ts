import { NextRequest, NextResponse } from 'next/server'
import { v2 as cloudinary } from 'cloudinary'

import { supabaseAdmin } from '@/lib/supabase'
import { logError, logApi } from '@/lib/logger'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
})

// GET - Fetch all gallery photos
export async function GET() {
  try {
    logApi('GET', '/api/gallery', undefined, undefined)

    const { data: photos, error } = await supabaseAdmin
      .from('gallery_photos')
      .select('*')
      .order('upload_date', { ascending: false })

    if (error) {
      logError('Gallery fetch error', error)
      return NextResponse.json(
        { error: 'Failed to fetch gallery photos' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      photos: photos || []
    })
  } catch (error) {
    logError('Gallery API error', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Upload new gallery photo
export async function POST(request: NextRequest) {
  try {
    logApi('POST', '/api/gallery', undefined, undefined)

    const formData = await request.formData()
    const file = formData.get('file') as File
    const title = formData.get('title') as string
    const description = formData.get('description') as string

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!title || title.trim().length === 0) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      )
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique public_id for Cloudinary
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const publicId = `revantad-gallery/photo_${timestamp}_${randomString}`

    // Upload to Cloudinary
    const uploadResult = await new Promise<{
      secure_url: string
      public_id: string
      width: number
      height: number
      bytes: number
    }>((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: publicId,
          folder: 'revantad-gallery',
          transformation: [
            { width: 1200, height: 1200, crop: 'limit' },
            { quality: 'auto:good' },
            { format: 'auto' }
          ],
          timeout: 30000
        },
        (error, result) => {
          if (error) {
            logError('Cloudinary upload error', error)
            reject(new Error(`Cloudinary upload failed: ${error.message}`))
          } else if (result) {
            resolve({
              secure_url: result.secure_url,
              public_id: result.public_id,
              width: result.width,
              height: result.height,
              bytes: result.bytes
            })
          } else {
            reject(new Error('Upload failed: No result returned from Cloudinary'))
          }
        }
      ).end(buffer)
    })

    // Save to database
    console.log('Attempting to save to database with data:', {
      title: title.trim(),
      description: description?.trim() || null,
      image_url: uploadResult.secure_url,
      image_public_id: uploadResult.public_id,
      file_size: uploadResult.bytes,
      image_width: uploadResult.width,
      image_height: uploadResult.height,
      likes_count: 0
    })

    const { data: photo, error: dbError } = await supabaseAdmin
      .from('gallery_photos')
      .insert({
        title: title.trim(),
        description: description?.trim() || null,
        image_url: uploadResult.secure_url,
        image_public_id: uploadResult.public_id,
        file_size: uploadResult.bytes,
        image_width: uploadResult.width,
        image_height: uploadResult.height,
        likes_count: 0
      })
      .select()
      .single()

    if (dbError) {
      // If database save fails, clean up the uploaded image
      try {
        await cloudinary.uploader.destroy(uploadResult.public_id)
      } catch (cleanupError) {
        logError('Failed to cleanup uploaded image after database error', cleanupError)
      }

      // Log detailed error information
      console.error('Database save error details:', {
        error: dbError,
        errorType: typeof dbError,
        errorString: String(dbError),
        errorJSON: JSON.stringify(dbError, null, 2),
        code: dbError?.code,
        message: dbError?.message,
        details: dbError?.details,
        hint: dbError?.hint,
        insertData: {
          title: title.trim(),
          description: description?.trim() || null,
          image_url: uploadResult.secure_url,
          image_public_id: uploadResult.public_id,
          file_size: uploadResult.bytes,
          image_width: uploadResult.width,
          image_height: uploadResult.height,
          likes_count: 0
        }
      })

      logError('Database save error', dbError)

      const errorMessage = dbError?.message || String(dbError) || 'Unknown database error'
      return NextResponse.json(
        { error: `Failed to save photo to database: ${errorMessage}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      photo
    })
  } catch (error) {
    logError('Gallery upload error', error)

    let errorMessage = 'Failed to upload photo'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}

// DELETE - Delete gallery photo
export async function DELETE(request: NextRequest) {
  try {
    console.log('DELETE request received for gallery photo')
    const { searchParams } = new URL(request.url)
    const photoId = searchParams.get('id')

    console.log('Photo ID from URL params:', photoId)
    console.log('Full URL:', request.url)

    if (!photoId) {
      console.error('No photo ID provided')
      return NextResponse.json(
        { error: 'Photo ID is required' },
        { status: 400 }
      )
    }

    logApi('DELETE', '/api/gallery', undefined, undefined)

    // Get photo details first
    const { data: photo, error: fetchError } = await supabaseAdmin
      .from('gallery_photos')
      .select('image_public_id')
      .eq('id', photoId)
      .single()

    if (fetchError || !photo) {
      return NextResponse.json(
        { error: 'Photo not found' },
        { status: 404 }
      )
    }

    // Delete from Cloudinary
    try {
      await cloudinary.uploader.destroy(photo.image_public_id)
    } catch (cloudinaryError) {
      logError('Cloudinary delete error', cloudinaryError)
      // Continue with database deletion even if Cloudinary fails
    }

    // Delete from database (this will also delete related likes due to CASCADE)
    const { error: deleteError } = await supabaseAdmin
      .from('gallery_photos')
      .delete()
      .eq('id', photoId)

    if (deleteError) {
      logError('Database delete error', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete photo from database' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Photo deleted successfully'
    })
  } catch (error) {
    logError('Gallery delete error', error)
    return NextResponse.json(
      { error: 'Failed to delete photo' },
      { status: 500 }
    )
  }
}
