/**
 * Database Verification Script
 * Verifies appearance_settings table and data consistency
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function verifyDatabase() {
  console.log('🔍 Starting Database Verification...\n')

  try {
    // Test 1: Check if we can connect to the database
    console.log('1️⃣ Testing database connection...')
    const { data: connectionTest, error: connectionError } = await supabase
      .from('appearance_settings')
      .select('count', { count: 'exact', head: true })

    if (connectionError) {
      console.log('❌ Database connection failed:', connectionError.message)
      return false
    }
    console.log('✅ Database connection successful')

    // Test 2: Check table structure and data
    console.log('\n2️⃣ Checking appearance_settings table...')
    const { data: settings, error: settingsError } = await supabase
      .from('appearance_settings')
      .select('*')

    if (settingsError) {
      console.log('❌ Error accessing appearance_settings:', settingsError.message)
      return false
    }

    console.log(`✅ Found ${settings.length} records in appearance_settings table`)
    
    if (settings.length > 0) {
      console.log('\n📋 Current settings:')
      settings.forEach(setting => {
        console.log(`   Key: ${setting.setting_key}`)
        console.log(`   Value: ${JSON.stringify(setting.setting_value, null, 2)}`)
        console.log(`   Updated: ${setting.updated_at}`)
        console.log('   ---')
      })
    }

    // Test 3: Check for landing page background setting specifically
    console.log('\n3️⃣ Checking landing page background setting...')
    const { data: bgSetting, error: bgError } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
      .single()

    if (bgError && bgError.code !== 'PGRST116') {
      console.log('❌ Error fetching background setting:', bgError.message)
      return false
    }

    if (!bgSetting) {
      console.log('⚠️  No landing page background setting found')
      
      // Create default setting
      console.log('🔧 Creating default background setting...')
      const defaultSetting = {
        setting_key: 'landing_page_background',
        setting_value: {
          backgroundType: 'gradient',
          gradientStyle: 'hero-gradient',
          customColors: {
            primary: '#22c55e',
            secondary: '#facc15'
          },
          backgroundImage: null,
          backgroundImagePublicId: null
        }
      }

      const { error: insertError } = await supabase
        .from('appearance_settings')
        .insert(defaultSetting)

      if (insertError) {
        console.log('❌ Failed to create default setting:', insertError.message)
        return false
      }
      console.log('✅ Default background setting created')
    } else {
      console.log('✅ Landing page background setting found:')
      console.log(`   Background Type: ${bgSetting.setting_value.backgroundType}`)
      console.log(`   Gradient Style: ${bgSetting.setting_value.gradientStyle}`)
      if (bgSetting.setting_value.backgroundImage) {
        console.log(`   Background Image: ${bgSetting.setting_value.backgroundImage}`)
        console.log(`   Public ID: ${bgSetting.setting_value.backgroundImagePublicId}`)
      }
    }

    // Test 4: Test write operations
    console.log('\n4️⃣ Testing write operations...')
    const testUpdate = {
      setting_value: {
        ...bgSetting?.setting_value || defaultSetting.setting_value,
        lastVerified: new Date().toISOString()
      }
    }

    const { error: updateError } = await supabase
      .from('appearance_settings')
      .update(testUpdate)
      .eq('setting_key', 'landing_page_background')

    if (updateError) {
      console.log('❌ Write operation failed:', updateError.message)
      return false
    }
    console.log('✅ Write operation successful')

    console.log('\n🎉 Database verification completed successfully!')
    return true

  } catch (error) {
    console.log('❌ Verification failed with error:', error.message)
    return false
  }
}

// Run verification
verifyDatabase().then(success => {
  process.exit(success ? 0 : 1)
})
