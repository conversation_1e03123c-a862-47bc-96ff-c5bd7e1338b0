/**
 * Cloudinary Setup Script
 * Creates the required upload presets for the application
 */

const https = require('https')
const querystring = require('querystring')

// Environment variables
const CLOUD_NAME = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'deraruhvk'
const API_KEY = process.env.CLOUDINARY_API_KEY || '646497165374356'
const API_SECRET = process.env.CLOUDINARY_API_SECRET || '2CU_n83R4A-uwlJUAKTENXU5rVI'

/**
 * Create an upload preset in Cloudinary
 */
function createUploadPreset(presetName, folder, options = {}) {
  return new Promise((resolve, reject) => {
    const postData = querystring.stringify({
      name: presetName,
      unsigned: true,
      folder: folder,
      use_filename: true,
      unique_filename: true,
      overwrite: false,
      resource_type: 'image',
      allowed_formats: 'jpg,jpeg,png,gif,webp',
      max_file_size: 10485760, // 10MB
      quality: 'auto:good',
      fetch_format: 'auto',
      ...options
    })

    const auth = Buffer.from(`${API_KEY}:${API_SECRET}`).toString('base64')

    const requestOptions = {
      hostname: 'api.cloudinary.com',
      port: 443,
      path: `/v1_1/${CLOUD_NAME}/upload_presets`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Basic ${auth}`
      }
    }

    const req = https.request(requestOptions, (res) => {
      let data = ''

      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`✅ Upload preset '${presetName}' created successfully`)
          resolve(JSON.parse(data))
        } else {
          console.error(`❌ Failed to create upload preset '${presetName}': ${res.statusCode}`)
          console.error(data)
          reject(new Error(`HTTP ${res.statusCode}: ${data}`))
        }
      })
    })

    req.on('error', (error) => {
      console.error(`❌ Error creating upload preset '${presetName}':`, error)
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

/**
 * Check if upload preset exists
 */
function checkUploadPreset(presetName) {
  return new Promise((resolve, reject) => {
    const auth = Buffer.from(`${API_KEY}:${API_SECRET}`).toString('base64')

    const requestOptions = {
      hostname: 'api.cloudinary.com',
      port: 443,
      path: `/v1_1/${CLOUD_NAME}/upload_presets/${presetName}`,
      method: 'GET',
      headers: {
        'Authorization': `Basic ${auth}`
      }
    }

    const req = https.request(requestOptions, (res) => {
      let data = ''

      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ Upload preset '${presetName}' already exists`)
          resolve(true)
        } else if (res.statusCode === 404) {
          console.log(`ℹ️ Upload preset '${presetName}' does not exist`)
          resolve(false)
        } else {
          console.error(`❌ Error checking upload preset '${presetName}': ${res.statusCode}`)
          reject(new Error(`HTTP ${res.statusCode}: ${data}`))
        }
      })
    })

    req.on('error', (error) => {
      console.error(`❌ Error checking upload preset '${presetName}':`, error)
      reject(error)
    })

    req.end()
  })
}

/**
 * Main setup function
 */
async function setupCloudinary() {
  console.log('🚀 Setting up Cloudinary upload presets...')
  console.log(`📍 Cloud Name: ${CLOUD_NAME}`)

  if (!API_KEY || !API_SECRET) {
    console.error('❌ Missing Cloudinary API credentials')
    console.error('Please set CLOUDINARY_API_KEY and CLOUDINARY_API_SECRET environment variables')
    process.exit(1)
  }

  try {
    // Setup background images preset
    const backgroundPresetExists = await checkUploadPreset('revantad-backgrounds')
    if (!backgroundPresetExists) {
      await createUploadPreset('revantad-backgrounds', 'revantad/backgrounds', {
        transformation: [
          { quality: 'auto:good' },
          { fetch_format: 'auto' },
          { width: 1920, height: 1080, crop: 'limit' }
        ]
      })
    }

    // Setup products preset (if not exists)
    const productsPresetExists = await checkUploadPreset('sari-sari-products')
    if (!productsPresetExists) {
      await createUploadPreset('sari-sari-products', 'sari-sari-products', {
        transformation: [
          { quality: 'auto:good' },
          { fetch_format: 'auto' },
          { width: 800, height: 600, crop: 'limit' }
        ]
      })
    }

    console.log('🎉 Cloudinary setup completed successfully!')
    console.log('')
    console.log('📋 Upload Presets Created:')
    console.log('  • revantad-backgrounds (for background images)')
    console.log('  • sari-sari-products (for product images)')
    console.log('')
    console.log('✅ Your application should now be able to upload images!')

  } catch (error) {
    console.error('❌ Cloudinary setup failed:', error.message)
    process.exit(1)
  }
}

// Run setup if called directly
if (require.main === module) {
  setupCloudinary()
}

module.exports = { setupCloudinary, createUploadPreset, checkUploadPreset }
