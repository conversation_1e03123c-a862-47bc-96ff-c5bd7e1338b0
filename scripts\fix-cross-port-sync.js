/**
 * Fix Cross-Port Synchronization for Background Images
 * This script addresses the issue where different localhost ports show different backgrounds
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function fixCrossPortSync() {
  console.log('🔧 Starting Cross-Port Synchronization Fix...\n')

  try {
    // Step 1: Clear all localStorage data that might be causing conflicts
    console.log('1️⃣ Clearing localStorage conflicts...')
    console.log('   ℹ️  This will be handled by the browser refresh')

    // Step 2: Check current database state
    console.log('\n2️⃣ Checking current database state...')
    const { data: currentSettings, error: fetchError } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.log('❌ Error fetching current settings:', fetchError.message)
      return false
    }

    if (currentSettings) {
      console.log('✅ Current database settings:')
      console.log(`   Background Type: ${currentSettings.setting_value.backgroundType}`)
      console.log(`   Gradient Style: ${currentSettings.setting_value.gradientStyle}`)
      if (currentSettings.setting_value.backgroundImage) {
        console.log(`   Background Image: ${currentSettings.setting_value.backgroundImage}`)
        console.log(`   Public ID: ${currentSettings.setting_value.backgroundImagePublicId}`)
      } else {
        console.log('   Background Image: None (using gradient)')
      }
      console.log(`   Last Updated: ${currentSettings.updated_at}`)
    } else {
      console.log('⚠️  No settings found in database')
    }

    // Step 3: Force update the database with a timestamp to trigger real-time sync
    console.log('\n3️⃣ Triggering database sync...')
    
    const settingsToUpdate = currentSettings ? {
      ...currentSettings.setting_value,
      lastSyncFix: new Date().toISOString()
    } : {
      backgroundType: 'gradient',
      gradientStyle: 'hero-gradient',
      customColors: {
        primary: '#22c55e',
        secondary: '#facc15'
      },
      backgroundImage: null,
      backgroundImagePublicId: null,
      lastSyncFix: new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('appearance_settings')
      .update({
        setting_value: settingsToUpdate
      })
      .eq('setting_key', 'landing_page_background')

    if (updateError) {
      console.log('❌ Failed to update database:', updateError.message)
      return false
    }

    console.log('✅ Database sync triggered successfully')

    // Step 4: Verify the update
    console.log('\n4️⃣ Verifying sync...')
    const { data: verifySettings, error: verifyError } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
      .single()

    if (verifyError) {
      console.log('❌ Verification failed:', verifyError.message)
      return false
    }

    console.log('✅ Verification successful:')
    console.log(`   Background Type: ${verifySettings.setting_value.backgroundType}`)
    if (verifySettings.setting_value.backgroundImage) {
      console.log(`   Background Image: ${verifySettings.setting_value.backgroundImage}`)
    }
    console.log(`   Last Updated: ${verifySettings.updated_at}`)

    console.log('\n🎉 Cross-port synchronization fix completed!')
    console.log('\n📋 Next Steps:')
    console.log('   1. Refresh all browser tabs/windows')
    console.log('   2. Clear browser cache if needed (Ctrl+Shift+R)')
    console.log('   3. Test uploading a new background image')
    console.log('   4. Verify the same image appears on all ports')

    return true

  } catch (error) {
    console.log('❌ Fix failed with error:', error.message)
    return false
  }
}

// Run the fix
fixCrossPortSync().then(success => {
  process.exit(success ? 0 : 1)
})
