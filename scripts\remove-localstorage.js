/**
 * Remove localStorage Dependencies
 * Clean up localStorage data and ensure Cloudinary + Database only approach
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function removeLocalStorageDependencies() {
  console.log('🧹 Removing localStorage Dependencies...\n')

  try {
    // Step 1: Verify current database state
    console.log('1️⃣ Checking current database state...')
    const { data: currentSettings, error: fetchError } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.log('❌ Error fetching current settings:', fetchError.message)
      return false
    }

    if (currentSettings) {
      console.log('✅ Current database settings:')
      console.log(`   Background Type: ${currentSettings.setting_value.backgroundType}`)
      if (currentSettings.setting_value.backgroundImage) {
        console.log(`   Cloudinary Image: ${currentSettings.setting_value.backgroundImage}`)
        console.log(`   Public ID: ${currentSettings.setting_value.backgroundImagePublicId}`)
      } else {
        console.log('   Background: Gradient (no Cloudinary image)')
      }
    } else {
      console.log('⚠️  No settings found in database')
    }

    // Step 2: Ensure database has proper settings
    console.log('\n2️⃣ Ensuring database integrity...')
    
    if (!currentSettings) {
      // Create default settings if none exist
      const defaultSettings = {
        backgroundType: 'gradient',
        gradientStyle: 'hero-gradient',
        customColors: {
          primary: '#22c55e',
          secondary: '#facc15'
        },
        backgroundImage: null,
        backgroundImagePublicId: null,
        noLocalStorage: true,
        cloudinaryOnly: true
      }

      const { error: insertError } = await supabase
        .from('appearance_settings')
        .insert({
          setting_key: 'landing_page_background',
          setting_value: defaultSettings
        })

      if (insertError) {
        console.log('❌ Failed to create default settings:', insertError.message)
        return false
      }
      console.log('✅ Default settings created in database')
    } else {
      // Update existing settings to mark as localStorage-free
      const updatedSettings = {
        ...currentSettings.setting_value,
        noLocalStorage: true,
        cloudinaryOnly: true,
        lastCleanup: new Date().toISOString()
      }

      const { error: updateError } = await supabase
        .from('appearance_settings')
        .update({ setting_value: updatedSettings })
        .eq('setting_key', 'landing_page_background')

      if (updateError) {
        console.log('❌ Failed to update settings:', updateError.message)
        return false
      }
      console.log('✅ Settings updated to localStorage-free mode')
    }

    // Step 3: Verify Cloudinary configuration
    console.log('\n3️⃣ Verifying Cloudinary configuration...')
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
    const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET
    const apiKey = process.env.CLOUDINARY_API_KEY
    const apiSecret = process.env.CLOUDINARY_API_SECRET

    console.log(`   Cloud Name: ${cloudName ? '✅ ' + cloudName : '❌ Missing'}`)
    console.log(`   Upload Preset: ${uploadPreset ? '✅ ' + uploadPreset : '❌ Missing'}`)
    console.log(`   API Key: ${apiKey ? '✅ Configured' : '❌ Missing'}`)
    console.log(`   API Secret: ${apiSecret ? '✅ Configured' : '❌ Missing'}`)

    if (!cloudName || !uploadPreset || !apiKey || !apiSecret) {
      console.log('❌ Cloudinary configuration incomplete')
      return false
    }

    // Step 4: Test Cloudinary connectivity
    console.log('\n4️⃣ Testing Cloudinary connectivity...')
    try {
      const testResponse = await fetch(
        `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
        {
          method: 'POST',
          body: new FormData()
        }
      )
      
      if (testResponse.status === 400) {
        console.log('✅ Cloudinary endpoint accessible')
      } else {
        console.log(`⚠️  Unexpected response: ${testResponse.status}`)
      }
    } catch (error) {
      console.log('❌ Cloudinary connectivity test failed:', error.message)
      return false
    }

    console.log('\n🎉 localStorage dependencies removed successfully!')
    console.log('\n📋 New Architecture:')
    console.log('   🗄️  Database: Primary storage for settings')
    console.log('   ☁️  Cloudinary: Exclusive image storage')
    console.log('   🚫 localStorage: Completely removed')
    console.log('   🔄 Real-time: Database-driven sync across ports')

    console.log('\n🧹 Manual Cleanup Required:')
    console.log('   1. Open browser Developer Tools (F12)')
    console.log('   2. Go to Application > Local Storage')
    console.log('   3. Delete these keys if they exist:')
    console.log('      - appearance-settings-fallback')
    console.log('      - appearance-settings-sync')
    console.log('   4. Refresh all browser tabs')

    console.log('\n✅ System now uses Cloudinary + Database only!')

    return true

  } catch (error) {
    console.log('❌ Cleanup failed with error:', error.message)
    return false
  }
}

// Run the cleanup
removeLocalStorageDependencies().then(success => {
  process.exit(success ? 0 : 1)
})
