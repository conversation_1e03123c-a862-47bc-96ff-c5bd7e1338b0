/**
 * Sync Appearance Settings Across Ports
 * This script ensures both ports use the same background settings
 */

const { createClient } = require('@supabase/supabase-js')

// Supabase configuration (you'll need to update these)
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'your-supabase-url'
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-key'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

const SETTING_KEY = 'landing_page_background'

// Default settings
const DEFAULT_SETTINGS = {
  backgroundType: 'gradient',
  gradientStyle: 'hero-gradient',
  customColors: {
    primary: '#22c55e',
    secondary: '#facc15'
  },
  backgroundImage: null,
  backgroundImagePublicId: null
}

async function testDatabaseConnection() {
  console.log('🔌 Testing database connection...')
  
  try {
    const { data, error } = await supabase
      .from('appearance_settings')
      .select('*')
      .limit(1)

    if (error) {
      console.log('❌ Database connection failed:', error.message)
      return false
    }

    console.log('✅ Database connection successful')
    return true
  } catch (error) {
    console.log('❌ Database connection error:', error.message)
    return false
  }
}

async function getCurrentSettings() {
  console.log('📖 Reading current settings from database...')
  
  try {
    const { data, error } = await supabase
      .from('appearance_settings')
      .select('setting_value')
      .eq('setting_key', SETTING_KEY)
      .single()

    if (error) {
      console.log('⚠️ No settings found in database:', error.message)
      return null
    }

    console.log('✅ Current settings:', JSON.stringify(data.setting_value, null, 2))
    return data.setting_value
  } catch (error) {
    console.log('❌ Error reading settings:', error.message)
    return null
  }
}

async function saveSettings(settings) {
  console.log('💾 Saving settings to database...')
  
  try {
    const { error } = await supabase
      .from('appearance_settings')
      .upsert({
        setting_key: SETTING_KEY,
        setting_value: settings
      })

    if (error) {
      console.log('❌ Failed to save settings:', error.message)
      return false
    }

    console.log('✅ Settings saved successfully')
    return true
  } catch (error) {
    console.log('❌ Error saving settings:', error.message)
    return false
  }
}

async function syncSettings() {
  console.log('🔄 Starting appearance settings sync...')
  console.log('=' .repeat(50))

  // Test database connection
  const isConnected = await testDatabaseConnection()
  if (!isConnected) {
    console.log('\n❌ Cannot sync - database not accessible')
    console.log('📋 Please run the database fix script first:')
    console.log('   1. Copy contents of database/fix_appearance_settings.sql')
    console.log('   2. Paste in Supabase SQL Editor')
    console.log('   3. Click "Run"')
    return
  }

  // Get current settings
  const currentSettings = await getCurrentSettings()
  
  if (!currentSettings) {
    console.log('\n🎨 No settings found, creating default settings...')
    const success = await saveSettings(DEFAULT_SETTINGS)
    
    if (success) {
      console.log('✅ Default settings created successfully')
      console.log('🎉 Both ports will now use the same background!')
    } else {
      console.log('❌ Failed to create default settings')
    }
  } else {
    console.log('\n✅ Settings already exist in database')
    console.log('🎉 Both ports should use the same background!')
  }

  console.log('\n' + '=' .repeat(50))
  console.log('🚀 Sync complete! Refresh both ports to see consistent backgrounds.')
}

// Run the sync
if (require.main === module) {
  syncSettings().catch(console.error)
}

module.exports = { syncSettings, testDatabaseConnection, getCurrentSettings, saveSettings }
